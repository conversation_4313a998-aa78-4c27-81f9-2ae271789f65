"use strict";
console.log('========== Logger.js =================');

const winston = require('winston');
const { format } = winston;
const fs = require('fs');
const path = require('path');
const { sseBroadcast, SSEs } = require('./routes/sse');

// logging into RAM 
class MemoryArray {
    constructor(maxSize = 100) {
        this.logArray = [];
        this.maxSize = maxSize;
    }

    log(info) {
        const logEntry = {
            timestamp: info.timestamp,
            level: info.level,
            message: info.message
        }
        if (info.data) { logEntry.data = info.data; }
        if (info.stack) { logEntry.stack = info.stack; }
        
        if (this.logArray.length >= this.maxSize) {
            this.logArray.pop(); // Remove oldest entry
        }
        this.logArray.unshift(logEntry); // Add to the front
    }

    log_raw(logEntry) {
        this.logArray.push(logEntry);
    }



    query() {
        return this.logArray;
    }

    reset() {
        this.logArray = [];
    }
    getLength() {
        return this.logArray.length;
    }
}



// Logger  
class Logger {
    constructor(options = {}) {
        const {
            size = 100, // Max number of memory entries
            filename = 'logger', // Base filename 
            filesize = 5 * 1024, // 5kb default file size
            persist = false, // Load from file on startup
            storeOnly = [], // Level to store in file
            logDirectory = './logs',
            verboseStack = true, // Show complete stack trace
            enableConsole = true, // Log to console
            broadcastName = 'logs', // sse broadcast name
        } = options;

        this.size = size;
        this.filename = filename;
        this.filesize = filesize;
        this.persist = persist;
        this.storeOnly = storeOnly;
        this.logDirectory = logDirectory;
        this.verboseStack = verboseStack;
        this.enableConsole = enableConsole;
        this.broadcastName = broadcastName;

        this.memoryArray = new MemoryArray(size);
        this.transports = [];
        this.logger = null;

        this._setupTransports();
    }

    _setupTransports() {
        if (this.filename) {
            this.transports.push(new winston.transports.File({
                filename: `${this.logDirectory}/${this.filename}.log`,
                maxsize: this.filesize,
                maxFiles: 2,
                tailable: true, //  ensures proper rotation 
                format: format.combine(
                    format.timestamp({ format: 'HH:mm:ss' }),
                    format.printf((info) => {
                        const logObj = {
                            timestamp: info.timestamp,
                            level: info.level,
                            message: info.message
                        };
                        if (info.data) { logObj.data = info.data; }
                        if (info.stack) { logObj.stack = info.stack; }
                        return JSON.stringify(logObj);
                    })
                ),
                level: this.storeOnly.includes('info') ? 'info' :
                    this.storeOnly.includes('warn') ? 'warn' :
                        this.storeOnly.includes('error') ? 'error' : 'info',
            }));
        }

        if (this.enableConsole) {
            this.transports.push(new winston.transports.Console({
                level: 'info',
                format: format.combine(
                    format.colorize(),
                    format.timestamp({ format: 'HH:mm:ss' }),
                    format.printf(({ timestamp, level, message, data, stack }) => {
                        let output = `${timestamp} [${level}] ${message}`;
                        if (data) output += ` | Data: ${data}`;
                        if (stack) output += `\n   Error: ${stack}`;
                        return output;
                    })
                ),
            }));
        }

        this.logger = this.transports.length > 0 ? winston.createLogger({
            transports: this.transports
        }) : null;
    }

    readLastLinesSync(filePath, maxLines, callback) {
        if (!fs.existsSync(filePath)) return [];

        const stats = fs.statSync(filePath);
        const fileSize = stats.size;
        const bufferSize = 4096;
        const buffer = Buffer.alloc(bufferSize);
        const fd = fs.openSync(filePath, 'r');

        let linesProcessed = 0;
        let position = fileSize;
        let leftover = '';

        while (position > 0 && linesProcessed < maxLines) {
            const readSize = Math.min(bufferSize, position);
            position -= readSize;

            fs.readSync(fd, buffer, 0, readSize, position);
            const chunkData = buffer.toString('utf8', 0, readSize);
            const data = chunkData + leftover;
            const parts = data.split(/\r?\n/);

            leftover = parts.pop() || '';

            // lines in reverse order
            for (let i = parts.length - 1; i >= 0; i--) {
                if (linesProcessed >= maxLines) break;
                callback(parts[i]);
                linesProcessed++;
            }
        }

        // Handle leftover after all chunks
        if (leftover && linesProcessed < maxLines) {
            callback(leftover);
            linesProcessed++;
        }

        fs.closeSync(fd);
        return linesProcessed; // return number of lines processed
    }

    init() {
        if (this.persist && this.filename) {
            try {
                const fileCurrent = path.join(this.logDirectory, `${this.filename}.log`);
                const fileOld = path.join(this.logDirectory, `${this.filename}1.log`);
                let loadedCount = 0;
                const processLogEntry = (log) => {
                    if (!log.trim()) return;
                    this.memoryArray.log_raw(JSON.parse(log));
                }

                // Read from current file first (newest logs)
                if (fs.existsSync(fileCurrent)) {
                    loadedCount = this.readLastLinesSync(fileCurrent, this.size, processLogEntry);
                }

                // If we need more lines and the rotated file exists, read from it
                if (loadedCount < this.size && fs.existsSync(fileOld)) {
                    this.readLastLinesSync(fileOld, this.size - loadedCount, processLogEntry);
                }

                // most recent at index 0
                this.memoryArray.logArray
            } catch (error) {
                console.error("Error loading log file:", error);
            }
        }
    }

    _log(level, msg, data) {
        let logEntry = this.logFormat(msg, data, level, this.verboseStack);
        this.memoryArray.log(logEntry);
        if (this.logger) {
            this.logger.log(logEntry);
        }
        if (this.broadcastName) {
            console.log(`Broadcasting to SSE channel '${this.broadcastName}':`, logEntry);
            // ensure if sses has the broadcastName group
            if (SSEs[this.broadcastName]) {
                sseBroadcast(logEntry, this.broadcastName);
            } 
            // otherwise setup the group 
            else {
                console.log(`Creating new SSE group: ${this.broadcastName}`);
                SSEs[this.broadcastName] = { clients: new Set() };
                sseBroadcast(logEntry, this.broadcastName);
            }
        }
    }

    log(msg, data) {
        this._log('info', msg, data);
    }

    warn(msg, data) {
        this._log('warn', msg, data);
    }

    error(msg, data) {
        this._log('error', msg, data);
    }

    getLogs(limit=10 , offset = 0) { // add limit and offset params 
        return this.memoryArray.logArray.slice(offset, offset + limit );
    }

    reset() {
        this.memoryArray.reset();
    }

    query(indexed = 0, offset = 0) {
        return this.memoryArray.query().slice(indexed, indexed + offset || undefined);
    }

    logFormat(msg, data, level = 'error', verboseStack = false) {
        const r = {
            timestamp: Date.now(),
            level,
        };

        if (typeof msg === 'string') {
            r.message = msg;
        } else if (msg instanceof Error) {
            r.message = msg.message;
            if (verboseStack) { r.stack = msg.stack; }
            else { r.stack = msg.stack.split('\n').slice(0, 2).join('\n'); }
        }

        if (data !== undefined) {
            r.data = data;
        }
        return r;
    }
}


module.exports = {
    Logger
};

