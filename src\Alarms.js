//*********************************************************************************************
//* COPYRIGHT © 2025-, <PERSON> and Praevista Inc. - All Rights Reserved
//* NOTICE: All information contained herein is, and remains the property of <PERSON>,
//* Praevista Inc., and their suppliers, if any. The intellectual and technical concepts
//* contained herein are proprietary to <PERSON>, Praevista Inc., and their suppliers
//* and are protected by trade secret or copyright law. Dissemination of this information or
//* reproduction of this material is strictly forbidden unless prior written permission is
//* obtained from Michael <PERSON> and Praevista Inc.
//*********************************************************************************************
//
// export/Alarm.js
//
// An Alarm constructor class
//
//=============================================================================================
console.log('========== Alarm.js ================')
const { parseDuration, durationToString } = require('./Util.js')
const { timestamp, registers, projectSettings, alarms }     = require('./Global.js')
const EmailSender = require('./EmailSender.js') // Email sender module
// const Project = require('./Project.js') // This doesn't load properly
const fs = require('fs')

// console.log('=== Project', Project, Project.ProjectDefinitions); process.exit(0);

const statusRegisters = [] // Array of status registers to include in the Alert email

// DEPRECATED: all alarms available through Global.alarms
const AlarmsRegisters = {
	alarms: null,
	lockouts: null,
}

// Track the state of alarms and lockouts to prevent repeated emails from being sent
const AlarmsState = Object.assign({}, AlarmsRegisters) // Copy the structure of AlarmsRegisters

/**
 * Represents an Alarm with configurable properties and state tracking
 *
 * @class Alarm
 * @param {Object} options - Configuration options for the alarm
 * @param {string} [options.name='Alarm{index}'] - Name of the alarm
 * @param {number} [options.setpoint=0] - Threshold value for alarm triggering
 * @param {string} [options.compare='>'] - Comparison operator for triggering
 * @param {boolean} [options.lockout=false] - Whether the alarm is a lockout type
 * @param {string} [options.comment=''] - Additional comment for the alarm
 * @param {string} [options.units=''] - Units of the setpoint value
 * @param {number} [options.display=0] - Decimal places for display
 * @param {number} [options.duration=60] - Duration for alarm trigger in seconds
 */
class Alarm {
	/**
 	 * Initializes a new Alarm instance with specified configuration options
 	 *
 	 * @param {Object} options - Configuration options for the alarm
 	 * @param {string} [options.name='Alarm{index}'] - Name of the alarm, defaults to 'Alarm' + current alarm count
 	 * @param {number} [options.setpoint=0] - Threshold value for alarm triggering
 	 * @param {string} [options.compare='>'] - Comparison operator for triggering (e.g., '>', '<', '==')
 	 * @param {boolean} [options.lockout=false] - Whether the alarm is a lockout type
 	 * @param {string} [options.comment=''] - Additional comment for the alarm
 	 * @param {string} [options.units=''] - Units of the setpoint value
 	 * @param {number} [options.display=0] - Decimal places for display
 	 * @param {number} [options.duration=60] - Duration for alarm trigger in seconds
 	 */
	constructor(options) {
		const duration = options.duration || '1m'
		// These are set in the constructor and remain constant
		this.i        = alarms.length
		this.name     = options.name     || 'Alarm' + alarms.length  // Alarm Name
		this.disable  = options.disable  || false
		this.device   = options.device   || null
		this.register = options.register || null
		this.element  = options.element  || null
		this.ioServer = options.ioServer || null  // The IO Server to use for this Alarm (when the register is not defined)
		this.setpoint = options.setpoint || 0     // The setpoint value
		this.compare  = options.compare  || '>'   // The comparison operator
		this.lockout  = options.lockout  || false // If the Alarm is a lockout
		this.comment  = options.comment  || ''    // A comment to be displayed with the Alarm
		this.units    = options.units    || ''    // The units of the setpoint value
		this.display  = options.display  || 0     // The display decimal places
		this.duration = parseDuration(duration)    // The trigger duration of the event in seconds
		// These are set in real time
		this.alarm    = false // The alarm state (true = alarm has been triggered)
		this.alert    = false // alarm has been triggered but the email has not been sent
		this.active   = false // The condition causing the alarm is active
		this.value    = null  // The current value being compared
		this.start    = 0
		this.error    = false
		this.defaults = {
			setpoint: this.setpoint,
			disable: this.disable,
			duration: duration,
		}
		// alarms.push(this)
	}

	/**
 	 * Clears the state of the alarm/lockout
 	 *
 	 * @returns {number} 1 if the alarm was previously triggered, 0 otherwise
 	 */
	clear() {
		const r = this.alarm ? 1 : 0
		this.alarm  = false
		this.active = false
		this.start  = 0
		return r
	}

	service(val, opt) {
		if(typeof opt === 'object')
		{
			Object.assign(this, opt)
		}

		let value = val
		if(value === undefined)
		{
			try {
				value = registers[this.device][this.register]
			}
			catch(e) {
				return(e)
			}
		}

		if(typeof value === 'object')
		{
			if(this.element) // We are going to use something other than the value of the register
			{
				this.value = value[this.element]
			}
			else
			{
				this.value = value.value
				this.error = value.error
				if(value.error) // If there is a sensor error, disable the alarm
				{
					this.active = false
					this.start = 0
					return(new Error('Sensor Error'))
				}
			}
		}
		else
		{
			this.value = value
		}

		let cmp = false
		switch(this.compare)
		{
			case '<':
				cmp = this.value < this.setpoint
				break

			case '>':
				cmp = this.value > this.setpoint
				break

			case '<=':
				cmp = this.value <= this.setpoint
				break

			case '>=':
				cmp = this.value >= this.setpoint
				break

			case '=':
			case '==':
				cmp = this.value == this.setpoint
				break

			default:
				break
		}


		this.timestamp = timestamp.s

		if(cmp) // Becomes active
		{
			if(this.active)
			{
				if(!this.disable && this.start > 0 && (this.timestamp - this.start) > this.duration)
				{
					this.alarm = true
					this.alert = true
				}
			}
			else
			{
				this.start = this.timestamp
			}
		}
		else
		{
			this.start = 0
		}

		this.active = cmp

	}
}

// Generate the email body for the alert email and send it to the email address
function composeAlertEmail(test = false)
{
	const emailTemplate = fs.readFileSync( __dirname + '/templates/alert-email.html', 'utf8')

	// projectSettings.PROJECT_NAME
	// projectSettings.PROJECT_LOCATION
	// projectSettings.ALERT_EMAIL

	const TEST_NOTE = test ? '<div style="font-size: 16px; margin: 8px 0 0 0;">Note: This is a Test Email Only</div>' : ''

	let body = emailTemplate.replaceAll('%%PROJECT_NAME%%', projectSettings.PROJECT_NAME)
		.replaceAll('%%TEST_NOTE%%', TEST_NOTE)
		.replaceAll('%%PROJECT_LOCATION%%', projectSettings.PROJECT_LOCATION)
		.replaceAll('%%MAC_ADDRESS%%', projectSettings.MAC)

	// Alarm Status
	let myRows = ''

	alarms.forEach(alarm => {
		const clr = alarm.lockout ? 'red' : 'yellow'
		const tClass = alarm.lockout ? 'text-lock' : 'text-alarm'
		const aState = alarm.alarm ? (alarm.lockout ? 'Lockout' : 'Alarm') : (alarm.disable ? 'Disabled' : 'Ok') // Alarm State
		const aClass = alarm.alarm ? ' class="bg-' + clr + '"' : ''
		let val = ''
		if (alarm.value === null)
		{
			val = '-NA-'
		}
		else
		{
			try {
				val = typeof alarm.value == 'boolean' ? '' + alarm.value : alarm.value.toFixed(alarm.display || 1) + ' ' + alarm.units
			} catch (error) {
				// val = '-NA-'
				return
			};
		}
		const vClass = alarm.active ? 'bg-' + clr : 'bg-green'
		// const condition = alarm.comment + ' <span class="text-condition">' +  + '</span>'
		myRows += '<tr><td class="'+tClass+'">'
			+ alarm.comment + '</td><td style="text-align: right;">'
			+ alarm.compare + ' ' + alarm.setpoint + ' ' + alarm.units + ' '
			+ durationToString(alarm.duration).replaceAll(' ', '&nbsp;')
			+ '</td><td' + aClass +'>' + aState + '</td><td class="' + vClass + '">'
			+ val + '</td></tr>\n'
	})

	body = body.replaceAll('%%ALERT_ROWS%%', myRows)

	// Status Registers
	myRows = ''
	statusRegisters.forEach(reg => {
		myRows += '<tr><td>' + reg.device + '</td><td>' + reg.report + '</td><td>'
			+ reg.asText() + '</td><td>' + reg.asTextAlternate() + '</td></tr>\n'
	})

	body = body.replaceAll('%%STATUS_ROWS%%', myRows)

	return body
}

async function sendAlertEmail(test = false)
{
	try {
		// Create EmailSender instance
		const emailSender = new EmailSender({
			to: projectSettings.ALERT_EMAIL,
			// to: '<EMAIL>',
			subject: 'GeoSCADA Alert from: ' + projectSettings.PROJECT_NAME,
			body: composeAlertEmail(test)
		});

		// Send email
		console.log('Sending email...');
		const result = await emailSender.send();

		console.log('Email sent successfully!');
		console.log('Response:', result.response);
		return result

	} catch (error) {
		console.error('Error sending email:', error.message);
	}
}

function emailTest(msg)
{
	console.log('-------------------------------------------------------------------')
	console.log('-----------------------------  EMAIL  -----------------------------')
	console.log(msg)
	console.log('-------------------------------------------------------------------')
}

// Called on every polling interval
// Check for alarms and send email if necessary
function svc()
{
	let emailSent = false
	alarms.forEach(alarm => {
		const err = alarm.service()
		if(err)
		{
			console.log('Alarms.svc error', alarm.name, alarm.device, alarm.register, err)
		}
		// Handle Alert Emails
		if(alarm.alert)
		{
			alarm.alert = false
			if(alarm.disable)
			{
				return
			}
			if(alarm.lockout)
			{
				if(!AlarmsState.lockouts && !emailSent) // Only send one email per polling interval
				{
					// send email
					emailTest('Lockout: ' + alarm.name )
					emailSent = true
				}
				AlarmsState.lockouts = true
			}
			else
			{
				if(!AlarmsState.alarms && !emailSent) // Only send one email per polling interval
				{
					// send email
					emailTest('Alarm: ' + alarm.name )
					emailSent = true
				}
				AlarmsState.alarms = true
			}
		}
	})
	Object.keys(AlarmsRegisters).forEach(type => {
		try {
			if(AlarmsRegisters[type] !== null)
			{
				AlarmsRegisters[type].value = AlarmsState[type]
			}
		} catch (error) {}
	})
}

function doesRegisterExist(registers, device, register, index)
{
	if(typeof registers === 'object')
	{
		if(registers[device] === undefined)
		{
			return 'Invalid "device" at index ' + index + ': ' + device
		}

		if(registers[device][register] === undefined)
		{
			return 'Invalid "register" at index ' + index + ': ' + register
		}
		return registers[device][register]
	}

}

// Apply overides from the settings file
function applySettings(settingsFileName)
{
	try {

		// These are the properties we want to apply from the settings file
		const myProps = [
			{name: 'disable'},
			{name: 'duration', parse: parseDuration },
			{name: 'setpoint'}
		]

		// First, apply the defaults to all alarms
		alarms.forEach(alarm => {
			myProps.forEach(prop => {
				if(alarm.defaults[prop.name] !== undefined)
				{
					alarm[prop.name] = prop.parse ? prop.parse(alarm.defaults[prop.name]) : alarm.defaults[prop.name]
				}
			})
		})

		const settings = JSON.parse(fs.readFileSync( settingsFileName, 'utf8'))

		// Then, apply the settings overrides
		settings.forEach(setting => {
			const alarm = alarms.find(el => el.name === setting.name)

			if(alarm)
			{
				myProps.forEach(prop => {
					if(setting[prop.name] !== undefined)
					{
						alarm[prop.name] = prop.parse ? prop.parse(setting[prop.name]) : setting[prop.name]
						// console.log('~~~~>> applySettings:', prop, alarm, setting, alarms[0])
						// process.exit(1)
					}
				})
			}
		})
		// process.exit(1)
		return false
	} catch (error) {
		// console.error('Error reading settings file:', settingsFileName, error)
		// process.exit(1) // Exit if there is an error reading the settings file
		return error // No settings file found, nothing to apply
	}
}
// function applySettings(settingsFileName)
// {
// 	try {
// 		const settings = JSON.parse(fs.readFileSync( settingsFileName, 'utf8'))
// 		settings.forEach(setting => {
// 			const alarm = alarms.find(el => el.name === setting.name)

// 			const myProps = [
// 				{name: 'disable'},
// 				{name: 'duration', parse: parseDuration },
// 				{name: 'setpoint'}
// 			]
// 			myProps.forEach(prop => {
// 				// First, apply the defaults
// 				if(alarm.defaults[prop.name] !== undefined)
// 				{
// 					alarm[prop.name] = prop.parse ? prop.parse(alarm.defaults[prop.name]) : alarm.defaults[prop.name]
// 				}
// 				// Then, apply the settings overrides
// 				if(setting[prop.name] !== undefined)
// 				{
// 					alarm[prop.name] = prop.parse ? prop.parse(setting[prop.name]) : setting[prop.name]
// 					// console.log('~~~~>> applySettings:', prop, alarm, setting, alarms[0])
// 					// process.exit(1)
// 				}
// 			})
// 		})
// 		// process.exit(1)
// 		return false
// 	} catch (error) {
// 		console.error('Error reading settings file:', settingsFileName, error)
// 		// process.exit(1) // Exit if there is an error reading the settings file
// 		return error // No settings file found, nothing to apply
// 	}
// }

function init(arrayOfAlarms, settingsFileName)
{
	console.log('>>>>>Initializing Alarms---------------->>>>>>>>>>>>>>>>>>>>>>>>>>>>>>')
	const dict = []

	// Clear the existing alarms and registers
	// This is to ensure we start fresh
	alarms.length = 0
	Object.keys(AlarmsRegisters).forEach(type => {
		AlarmsRegisters[type] = null
	})
	Object.keys(AlarmsState).forEach(type => {
		AlarmsState[type] = null
	})


	arrayOfAlarms.forEach((al, index) => {

		// Special case of a register assignment
		// This is used to assign the alarms or lockouts registers
		if(al.assign)
		{
			const type = al.assign.type.toLowerCase()
			if(!type || typeof type !== 'string')
			{
				console.error('Alarms init Error: invalid assignment at index:' + index + ', parameter "type" is missing or invalid')
				return
			}
			if(type !== 'alarms' && type !== 'lockouts')
			{
				console.error('Alarms init Error: invalid "type" at index:' + index + ', expected "alarms" or "lockouts", got: ' + type)
				return
			}
			if(AlarmsRegisters[type] !== undefined)
			{
				const reg = doesRegisterExist(registers, al.assign.device, al.assign.register, index)
				if(typeof reg === 'string')
				{
					console.error('Alarms init Error:' + reg)
					return
				}
				AlarmsRegisters[al.assign.type] = reg
			}
			else
			{
				console.error('Alarms init Error: duplicate assignment for type "' + type + '" at index ' + index)
			}
			return
		}

		const alarm = Object.assign({}, al)
		if(dict.includes(alarm.name))
		{
			console.log('Duplicate alarm name at index ' + index + ': ' + alarm.name)
			return
		}
		// Validate the device/register
		if(registers)
		{
			const reg = doesRegisterExist(registers, alarm.device, alarm.register, index)
			if(typeof reg === 'string')
			{
				console.log(reg)
				return
			}

			if(alarm.element) // If the value to be compared is not the register.value
			{
				alarm.units = ''
			}
			else if(!alarm.units)
			{
				alarm.units = registers[alarm.device][alarm.register].units || ''
			}
		}
		dict.push(alarm.name)
		alarms.push(new Alarm(alarm))
	})

	// Create the list of status registers to include in the Alert email
	statusRegisters.length = 0
	Object.keys(registers).forEach(deviceKey => {
		const device = registers[deviceKey]
		Object.keys(device).forEach(registerKey => {
			const reg = device[registerKey]
			if(reg.report)
			{
				statusRegisters.push(reg)
			}
		})
	})

	applySettings(settingsFileName)

	console.log('>>>>> Initializing Alarms statusRegisters', statusRegisters)

	console.log('>>>>> Initializing Alarms alarms', alarms)
	// process.exit(0)
	return alarms
}

function clearAlarms(type)
{
	let n = 0
	const lo = (type + '').includes('lock') // we are only clearing lockouts

	// Reset the state of the alarms/lockouts so that emails can be sent again
	AlarmsState[lo ? 'lockouts' : 'alarms'] = false

	alarms.forEach(alarm => {
		if(alarm.lockout === lo)
		{
			n += alarm.clear()
		}
	})
	return n
}

module.exports = {
	Alarm,
	svc,
	init,
	applySettings,
	composeAlertEmail,
	sendAlertEmail,
	clearAlarms,
	alarms
}
