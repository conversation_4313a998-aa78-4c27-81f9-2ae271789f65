//*********************************************************************************************
//* COPYRIGHT © 2025-, <PERSON> and Praevista Inc. - All Rights Reserved
//* NOTICE: All information contained herein is, and remains the property of <PERSON>,
//* Praevista Inc., and their suppliers, if any. The intellectual and technical concepts
//* contained herein are proprietary to <PERSON>, Praevista Inc., and their suppliers
//* and are protected by trade secret or copyright law. Dissemination of this information or
//* reproduction of this material is strictly forbidden unless prior written permission is
//* obtained from Michael <PERSON> and Praevista Inc.
//*********************************************************************************************
//
// Datalog.js
//
//=============================================================================================
console.log('========== Datalog.js ==============')
const fs = require('fs')
const { projectSettings } = require('./Global.js')
const { sseBroadcast } = require('./routes/sse.js')

// const { DatalogList, ProjectDefinitions } = require('./Project.js')

// Metadata for a single datalog entry
// {
// 	"dtype": "F32",
// 	"group": "AnalogIn",
// 	"device": "AnalogIn",
// 	"register": "TS01",
// 	"units": "°C",
// 	"display": 2
// }

// Must be called after servers and registers loaded

//==============================================================================================
// Check directory and create if missing
//==============================================================================================
function directoryCheck(path) {
	try {
		const stat = fs.statSync(path)
	} catch (error) {
		fs.mkdirSync(path)
	}
}



function init(ProjectDefinitions, DatalogList, registers) {

	const metadataHeader = {
		macid: parseInt(projectSettings.MAC.replace(/[^0-9a-fA-F]/g, ''), 16),
		mac: projectSettings.MAC,
		model: 'Praevista-GeoSCADA-NR2',
		label: projectSettings.PROJECT_NAME,
		description: projectSettings.PROJECT_LOCATION,
		// registers: [],
	}

	const datalogSettings = Object.assign({
		enabled: true,
		intervalFine: 60,
		intervalCoarse: 60 * 5,
		path: '/public/datalog',
	}, ProjectDefinitions.datalog)

	const paths = {
		root: datalogSettings.path,
		fine: datalogSettings.path + '/fine',
		coarse: datalogSettings.path + '/coarse',
		metadata: datalogSettings.path + '/metadata.json',
		metacsv: datalogSettings.path + '/metadata.csv',
		persist: datalogSettings.path + '/persist.json',
	}

	datalogSettings.paths = paths

	directoryCheck(paths.root)
	directoryCheck(paths.fine)
	directoryCheck(paths.coarse)

	// Make sure the file system is prepared, create directories as required


	// Clean/check/decorate this list before using it

	const list = []
	const persist = []
	const metadata = Object.assign({}, metadataHeader, { registers: [] })

	DatalogList.forEach((reg, i) => {

		let meta = {} // placeholder for metadata

		const placeholder = !reg.device && !reg.register

		let regObj = null

		const dev = registers[reg.device]
		if (dev) {
			regObj = dev[reg.register] || null
			if (regObj) {
				if (reg.persist) {
					persist.push(regObj)
					// if(persist[reg.device] === undefined)
					// {
					// 	persist[reg.device] = {}
					// }
					// persist[reg.device][reg.register] = regObj
				}
				meta = {
					index: i + 1,
					dtype: reg.dtype,
					group: regObj.group || reg.device,
					device: reg.device,
					register: reg.register,
					units: regObj.units,
					display: regObj.display,
				}
			}
			else if (!placeholder) {
				console.log('Datalog init DatalogList[' + i + ']: `register` "' + reg.device + '.' + reg.register + '" not found')
			}
		}
		else if (!placeholder) {
			console.log('Datalog init DatalogList[' + i + ']: `device` "' + reg.device + '" not found')
		}

		if (!reg.disable) {
			list.push(regObj)
			metadata.registers.push(meta)
		}
	})

	datalogSettings.list = list
	datalogSettings.persist = persist
	datalogSettings.metadata = metadata

	return datalogSettings
}

function render(timestamp, list) {
	let r = '' + timestamp.s
	const a = [timestamp.s]
	list.forEach((reg, i) => {

		const val = reg ? reg._value : null
		const type = typeof val
		if (type === 'number') {
			let tmp
			if (typeof reg.display === 'number') {
				const str = val.toFixed(reg.display)
				if (reg.display === 0) {
					tmp = str
				}
				else {
					tmp = str.replace(/\.?0+$/, '')
				}
			}
			else {
				tmp = val
			}
			a.push(tmp)
			r += ',' + tmp
		}
		else if (type === 'boolean') {
			r += val ? ',1' : ',0'
			a.push( val ? 1 : 0)
		}
		else if (type === 'string') {
			r += ',' + val
			a.push(val)
		}
		else {
			r += ','
			a.push(null)
		}
	})
	return { string: r + '\n', array: a }
}

// Build the object that needs to be persisted
function renderPersist(persistList) {
	const r = {}
	persistList.forEach((reg, i) => {
		// console.log('Datalog renderPersist', i, reg)
		if (r[reg.device] === undefined) {
			r[reg.device] = {}
		}
		r[reg.device][reg.name || reg.register] = reg._value
	})
	return r
}


// Restore the persisted registers from the file system
function restorePersist(filePath, registers) {
	// Update Metadata
	try {
		const pStr = fs.readFileSync(filePath, 'utf8')
		const pObj = JSON.parse(pStr)
		Object.keys(pObj).forEach(devKey => {
			if (registers[devKey]) {
				Object.keys(pObj[devKey]).forEach(regKey => {
					registers[devKey][regKey]._value = pObj[devKey][regKey]
				})
			}
		})
	} catch (error) {
		if (error.code !== 'ENOENT') {
			console.error('Datalog restorePersist', error)
		}
		return
	}
}

let lastSvcTimestamp = 0

// call this for every polling cycle
function svc(timestamp, datalogSettings) {

	// console.log('Datalog svc', renderPersist(datalogSettings.persist))

	if (datalogSettings.enabled && timestamp.s !== lastSvcTimestamp && timestamp.s % datalogSettings.intervalFine === 0) {
		lastSvcTimestamp = timestamp.s
		const day = parseInt(timestamp.s / 86400) // The day number
		const fileFine = datalogSettings.paths.fine + '/D' + day + '.csv'
		const data = render(timestamp, datalogSettings.list)
		console.log('Datalog.svc', fileFine, data.string)
		fs.appendFile(fileFine, data.string, (err) => {
			if (err) console.error('Datalog svc', err)
		})

		let coarse = false;
		if (timestamp.s % datalogSettings.intervalCoarse === 0) {
			const fileCoarse = datalogSettings.paths.coarse + '/DC' + day + '.csv'
			console.log('Datalog.svc', fileCoarse, data.string)
			fs.appendFile(fileCoarse, data.string, (err) => {
				if (err) console.error('Datalog svc', err)
			})

			// Write the persistent registers
			savePersist(datalogSettings)
			coarse = true;
		}

		sseBroadcast({ t: timestamp.s, fine: true, coarse }, 'datalog');

		return { data: data.array, string:data.string, coarse }
	}
	return null
}

// Example firstColumn = [ 'Time', 'America/Toronto', 'YYYY-MM-DD HH:mm:ss', ]

function savePersist(datalogSettings)
{
	const persist = renderPersist(datalogSettings.persist)
	fs.writeFileSync(datalogSettings.paths.persist, JSON.stringify(persist, null, '\t'))

}

function metadataToCsv(metadata, firstColumn) {
	let r = ''
	const myFirstColumn = firstColumn || []
	try {
		Object.keys(metadata).forEach((key, i) => {
			if (key != 'registers') {
				r += metadata[key] + '\n'
			}
		})

		const list = ["dtype", "group", "device", "register", "units", "display"]

		list.forEach((key, iy) => {
			r += myFirstColumn[iy] || ''
			metadata.registers.forEach((reg, ix) => {
				r += ',' + (reg[key] === undefined ? '' : reg[key])
			})
			r += '\n'
		})
	} catch (error) {
		console.error('Datalog metadataToCsv', error)
	}
	return r
}

module.exports = {
	init,
	render,
	svc,
	savePersist,
	restorePersist,
	metadataToCsv,
}

