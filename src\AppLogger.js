const { Logger } = require('./Logger.js');

// loggers
const startupLogger = new Logger({ 
    size: 100,
    filename: 'startup',
    filesize: 3 * 1024,
    persist: true,
    storeOnly: ['error', 'warn', 'info'],
    verboseStack: false,
    enableConsole: true,
    // broadcastName: 'startup',
});

const systemLogger = new Logger({
    size: 100,
    filename: 'system',
    filesize: 10 * 1024 * 1024,
    storeOnly: ['error', 'warn', 'info'],
    verboseStack: false,
    enableConsole: true,
    broadcastName: 'system', 
});

const alertLogger = new Logger({
    size: 100,
    filename: 'alerts',
    filesize: 10 * 1024 * 1024,
    broadcastName: 'alerts',
});

module.exports = { startupLogger, systemLogger, alertLogger };

// test case - enable this to test systemLogger
systemLogger.log('TEST: System logger is working');
systemLogger.warn('TEST: Warning message');
console.log('System logger test messages sent');

// startupLogger.init();
// console.log("before logging stuff", startupLogger.getLogs(limit = 10, offset = 0));
// console.log("length before", startupLogger.memoryArray.getLength());

// startupLogger.log('System started mama mia >>> logg added');

// // startupLogger.warn('warning message with object', { a: 1, b: 2, c: 3 });
// // startupLogger.error(new Error('Test error'));

// console.log("after logging stuff",startupLogger.getLogs());