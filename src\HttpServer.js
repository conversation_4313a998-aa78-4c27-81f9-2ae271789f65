//*********************************************************************************************
//* COPYRIGHT © 2025-, <PERSON> and Praevista Inc. - All Rights Reserved
//* NOTICE: All information contained herein is, and remains the property of <PERSON>,
//* Praevista Inc., and their suppliers, if any. The intellectual and technical concepts
//* contained herein are proprietary to <PERSON>, Praevista Inc., and their suppliers
//* and are protected by trade secret or copyright law. Dissemination of this information or
//* reproduction of this material is strictly forbidden unless prior written permission is
//* obtained from Michael <PERSON> and Praevista Inc.
//*********************************************************************************************
//
// HttpServer.js
//
// The top level Fastify server
//=============================================================================================
console.log('========== HttpServer.js ===========')


const Fastify = require('fastify')
const fastifyStatic = require('@fastify/static')
const cors = require('@fastify/cors')
const path = require('path')

const { sseRoutes, sseClose } = require('./routes/sse.js')
const loginRoutes  = require('./routes/login.js')
const dataRoutes   = require('./routes/data.js')
const statusRoutes = require('./routes/status.js')
const setupRoutes  = require('./routes/setup.js')
const alarmsRoutes = require('./routes/alarms.js')
const logsRoutes   = require('./routes/logs.js')
const { ProjectDefinitions } = require('./Project.js')

const DEBUG = false

const serverOptions = {
	port:  3880,
	host: '0.0.0.0',
	ignoreTrailingSlash: true,
}

let fastify = null

async function init()
{

	// Close previous server if open
	await close()

	fastify = Fastify({ logger: DEBUG })

	//----------------------------------------------------------------------------------------------------
	// Enable CORS
	// See: https://github.com/fastify/fastify-cors for options
	//----------------------------------------------------------------------------------------------------
	await fastify.register(cors, {
		origin: true, // or specific origins ['https://example.com']
		methods: ['GET', 'PUT', 'POST', 'DELETE', 'OPTIONS'],
		allowedHeaders: ['Content-Type', 'Authorization']
})

	//----------------------------------------------------------------------------------------------------
	// Static Files (for serving the Quasar web app)
	// Serve static files from the "serve" directory
	// See: https://github.com/fastify/fastify-static
	//----------------------------------------------------------------------------------------------------
	await fastify.register(fastifyStatic, {
		// root: path.resolve('./serve'),
		root: '/public/serve',
		prefix: '/', // optional: default '/'
		// constraints: { host: 'example.com' } // optional: default {}
	})

	// `docs` directory
	await fastify.register(fastifyStatic, {
		root: '/public/project/Roxborough/docs', // TODO: use ProjectDefinitions.docsDirectory
		// root: ProjectDefinitions.docsDirectory,
		prefix: '/docs/', // optional: default '/'
		decorateReply: false, // the reply decorator has been added by the first plugin registration
	})


	//----------------------------------------------------------------------------------------------------
	// CRUD API routes
	
	//----------------------------------------------------------------------------------------------------
	// fastify.register(configRoutes, {})  // /api/config/*
	// fastify.register(infoRoutes,  {})  // /info/ip

	fastify.register(dataRoutes,   {})   // /data
	fastify.register(statusRoutes, {})   // /status
	fastify.register(setupRoutes,  {})   // /setup
	fastify.register(alarmsRoutes, {})   // /alarms
	fastify.register(logsRoutes,   {})   // /logs

	fastify.register(sseRoutes,    {})  // /api/sse
	fastify.register(loginRoutes,  {})  // /api/login



	// Declare a route
	fastify.get('/api', async function handler (request, reply) {
		return { hello: 'world', time: Date().now() }
	})

	fastify.get('/xxx', async function handler (request, reply) {
		return '__dirname: ' + __dirname + '\r\n' + path.resolve('./serve')
	})

	// fastify.get('/close', async function handler (request, reply) {
	// 	gracefulShutdown()
	// 	// return 'Closing down'
	// })

	// Run the server!
	return fastify.listen(serverOptions)
	.then(() => {
		console.log('++++++ Server is listening on port', serverOptions.port)
		return fastify
	})
	.catch((err) => {
		console.error(err)
		process.exit(1)
	})
}

function close()
{
	if(fastify)
	{
		console.log('Trying to close HTTP Server')
		sseClose()
		return fastify.close().then(() => {
			fastify = null
			console.log('HTTP Server closed')
		}).catch(err => {
			console.error(err)
		})
	}
	console.log('HTTP Server was already closed')
	return Promise.resolve()
}


module.exports = {
	init,
	close,
}
