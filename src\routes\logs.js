

//*********************************************************************************************
//* COPYRIGHT © 2025-, <PERSON> and Praevista Inc. - All Rights Reserved
//* NOTICE: All information contained herein is, and remains the property of <PERSON>,
//* Praevista Inc., and their suppliers, if any. The intellectual and technical concepts
//* contained herein are proprietary to <PERSON>, Praevista Inc., and their suppliers
//* and are protected by trade secret or copyright law. Dissemination of this information or
//* reproduction of this material is strictly forbidden unless prior written permission is
//* obtained from Michael <PERSON> and Praevista Inc.
//*********************************************************************************************
//
// routes/logs.js
//
// System Logs routes
//=============================================================================================
console.log('========== routes/logs.js ==========')

/**
 * @module routes/logs
 */

const { API_ROOT } = require('../System.js')
const { startupLogger } = require('../AppLogger.js')
// const { objectArrayInclude, objectArrayExclude, stringToBoolean } = require('../Util.js')
// const { AUTH_OPERATOR } = require('../AppLoginAuth.js')
// const { postFileHandler } = require('./handlers.js')
// const { ProjectDefinitions } = require('../Project.js')

function logFormat(msg, data, level='error')
{
	const r = {
		timestamp: Date.now(),
		level,
	}

	if(data !== undefined)
	{
		try {
			r.data = JSON.stringify(data)
		} catch (error) {}
	}

	if(typeof msg === 'string')
	{
		r.message = msg
		return r
	}
	else if(msg instanceof Error)
	{
		r.message = msg.message
		r.stack = msg.stack
		return r
	}
}

//----------------------------------------------------------------------------------------------------
/**
 * Routes for the System Logs page.
 * - Call using `fastify.register()`
 * @param {object} fastify - The Fastify instance
 * @param {object} options
 * @param {Function} done - The callback function to call when the plugin is registered
 */
//----------------------------------------------------------------------------------------------------
function logsRoutes(fastify, options, done)
{

	// GET /api/status/logs/init
	fastify.get(API_ROOT + '/status/logs/init', async (request, reply) => {
		const r = [
			logFormat(new Error('Example Error 1')),
			logFormat(new Error('Example Error 2'), undefined, 'warning'),
			logFormat('Example Message 3', undefined, 'info'),
			logFormat('This is how JavaScript variable is stored', { a:1, b:2 }, 'info'),
		]
		return reply.send(r)
	})


    // GET /api/status/logs/startup : newest to oldest (top to bottom)
    fastify.get(API_ROOT + '/status/logs/startup', async (request, reply) => { // newest to oldest (top to bottom)
		const { limit = 10, offset = 0 } = request.query 
        startupLogger.init()
		const r = startupLogger.getLogs(limit, offset)
		return reply.send(r)
	})


	done()
}


module.exports = logsRoutes
