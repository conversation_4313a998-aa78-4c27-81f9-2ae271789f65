// /public/src/Logger.js
const winston = require('winston');
const path = require('path');
const fs = require('fs');
const MemoryTransport = require('./transports--ignore/MemoryTransports.js');

/**
 * Embedded System Logger with three log types: Startup, Operational, and Alerts
 * Each log type has both memory-based FIFO storage and file-based storage with rotation
 */
class EmbeddedSystemLogger {
    constructor(options = {}) {
        // Default configuration - will be overridden by systemSettings
        this.config = {
            logDir: options.logDir || path.resolve(__dirname, '../logs'),

            // Startup log configuration
            startup: {
                memoryMaxEntries: options.startupMemoryMaxEntries || 500,
                fileMaxSize: options.startupFileMaxSize || 1 * 1024 * 1024, // 1MB
                maxFiles: 2
            },

            // Operational log configuration
            operational: {
                memoryMaxEntries: options.operationalMemoryMaxEntries || 1000,
                fileMaxSize: options.operationalFileMaxSize || 2 * 1024 * 1024, // 2MB
                maxFiles: 2
            },

            // Alerts log configuration
            alerts: {
                memoryMaxEntries: options.alertsMemoryMaxEntries || 200,
                fileMaxSize: options.alertsFileMaxSize || 512 * 1024, // 512KB
                maxFiles: 2
            },

            consoleLogLevel: options.consoleLogLevel ||
                (process.env.NODE_ENV === 'production' ? 'info' : 'debug')
        };

        // Create log directory
        this._ensureLogDirectory();

        // Initialize the three logging systems
        this.loggers = {};
        this.memoryTransports = {};
        this._createLoggers();

        // Add unified access methods
        this._addAccessMethods();
    }

    /**
     * Ensure log directory exists
     */
    _ensureLogDirectory() {
        try {
            if (!fs.existsSync(this.config.logDir)) {
                fs.mkdirSync(this.config.logDir, { recursive: true });
                console.log(`EmbeddedLogger: Created log directory at: ${this.config.logDir}`);
            }
        } catch (error) {
            console.error(`EmbeddedLogger: Failed to create log directory: `, error.message);
        }
    }

    /**
     * Create separate loggers for each log type (Startup, Operational, Alerts)
     */
    _createLoggers() {
        const logTypes = ['startup', 'operational', 'alerts'];

        logTypes.forEach(logType => {
            // Create memory transport for this log type
            this.memoryTransports[logType] = new MemoryTransport({
                level: 'info',
                maxEntries: this.config[logType].memoryMaxEntries,
                logType: logType
            });

            // Create Winston logger for this log type
            this.loggers[logType] = winston.createLogger({
                levels: {
                    fatal: 0,    // For startup fatal errors
                    error: 1,
                    warn: 2,
                    info: 3,
                    debug: 4
                },
                format: winston.format.combine(
                    winston.format.timestamp({ format: 'YYYY-MM-DD HH:mm:ss' }),
                    winston.format.json()
                ),
                transports: [
                    this.memoryTransports[logType],
                    new winston.transports.File({
                        filename: path.join(this.config.logDir, `${logType}.log`),
                        level: 'info',
                        format: this._createFileLogFormat(),
                        maxsize: this.config[logType].fileMaxSize,
                        maxFiles: this.config[logType].maxFiles,
                        tailable: true
                    }),
                    new winston.transports.Console({
                        level: this.config.consoleLogLevel,
                        format: this._createConsoleFormat(logType)
                    })
                ],
                exitOnError: false
            });
        });
    }

    /**
     * Create file log format for embedded system logs
     */
    _createFileLogFormat() {
        return winston.format.printf(info => {
            // Extract simplified log entry if available
            if (info.timestamp && typeof info.timestamp === 'number') {
                return JSON.stringify({
                    timestamp: info.timestamp,
                    level: info.level,
                    message: info.message,
                    ...(info.error && { error: info.error })
                });
            } else {
                // Fallback format
                return JSON.stringify({
                    timestamp: Date.now(),
                    level: info.level,
                    message: info.message
                });
            }
        });
    }

    /**
     * Create console format with log type identification
     */
    _createConsoleFormat(logType) {
        return winston.format.printf(info => {
            const time = new Date().toTimeString().substring(0, 8);
            // Use the simplified log entry if available
            return `[${time}] [${logType.toUpperCase()}] [${info.level.toUpperCase()}] ${info.message}`;
            // if (info.timestamp && typeof info.timestamp === 'number') {
            // } else {
            //     return `[${time}] [${logType.toUpperCase()}] [${info.level.toUpperCase()}] ${info.message}`;
            // }
        });
    }

    /**
     * Extract metadata from log info, excluding standard winston fields
     */
    _extractMetadata(info) {
        const metadata = {};
        for (const key in info) {
            if (['level', 'message', 'timestamp', 'splat', 'stack',
                Symbol.for('level'), Symbol.for('message')].includes(key)) {
                continue;
            }

            const value = info[key];
            if (typeof value === 'function' || typeof value === 'symbol') {
                continue;
            } else if (typeof value === 'object' && value !== null) {
                if (value instanceof Error) {
                    metadata[key] = { name: value.name, message: value.message };
                } else {
                    try {
                        const stringified = JSON.stringify(value);
                        metadata[key] = stringified.length > 200 ?
                            `[Object size: ${stringified.length} chars]` : value;
                    } catch (e) {
                        metadata[key] = '[Complex Object]';
                    }
                }
            } else {
                metadata[key] = value;
            }
        }
        return metadata;
    }

    /**
     * Add unified access methods for   all log types
     */
    _addAccessMethods() {
        // Main logging methods for each type
        this.startup = (level, message, metadata = {}) => {
            this._logToType('startup', level, message, metadata);
        };

        this.operational = (level, message, metadata = {}) => {
            this._logToType('operational', level, message, metadata);
        };

        this.alerts = (level, message, metadata = {}) => {
            this._logToType('alerts', level, message, metadata);
        };

        // Convenience methods for startup logging
        this.startup.info = (message, metadata) => this.startup('info', message, metadata);
        this.startup.warn = (message, metadata) => this.startup('warn', message, metadata);
        this.startup.error = (message, metadata) => this.startup('error', message, metadata);
        this.startup.fatal = (message, metadata) => this.startup('fatal', message, metadata);

        // Convenience methods for operational logging
        this.operational.info = (message, metadata) => this.operational('info', message, metadata);
        this.operational.warn = (message, metadata) => this.operational('warn', message, metadata);
        this.operational.error = (message, metadata) => this.operational('error', message, metadata);

        // Convenience methods for alerts logging
        this.alerts.info = (message, metadata) => this.alerts('info', message, metadata);
        this.alerts.warn = (message, metadata) => this.alerts('warn', message, metadata);
        this.alerts.error = (message, metadata) => this.alerts('error', message, metadata);

        // Memory access methods
        this.getMemoryLogs = (logType) => {
            if (!this.memoryTransports[logType]) return [];
            return this.memoryTransports[logType].getLogs();
        };

        this.clearMemoryLogs = (logType) => {
            if (this.memoryTransports[logType]) {
                this.memoryTransports[logType].clearLogs();
            }
        };

        this.getAllMemoryLogs = () => {
            return {
                startup: this.getMemoryLogs('startup'),
                operational: this.getMemoryLogs('operational'),
                alerts: this.getMemoryLogs('alerts')
            };
        };

        // Configuration update methods
        this.updateConfig = (newConfig) => {
            Object.assign(this.config, newConfig);
            // Recreate loggers with new configuration
            this._createLoggers();
        };
    }

    /**
     * Internal method to log to a specific log type
     */
    _logToType(logType, level, message, metadata = {}) {
        if (!this.loggers[logType]) {
            console.error(`EmbeddedLogger: Unknown log type: ${logType}`);
            return;
        }

        // Create simplified log entry
        const logEntry = {
            timestamp: Date.now(),
            level: level,
            message: message
        };

        // Add error object if present in metadata
        if (metadata && metadata.error) {
            if (metadata.error instanceof Error) {
                logEntry.error = {
                    name: metadata.error.name,
                    message: metadata.error.message,
                    stack: metadata.error.stack
                };
            } else {
                logEntry.error = metadata.error;
            }
        } else if (metadata instanceof Error) {
            logEntry.error = {
                name: metadata.name,
                message: metadata.message,
                stack: metadata.stack
            };
        }

        // Log to Winston (pass the logEntry as the main object)
        this.loggers[logType].log(logEntry);

        // Broadcast via SSE if available
        this._broadcastLogEntry(logType, logEntry);
    }

    /**
     * Broadcast log entry via SSE for real-time updates
     */
    _broadcastLogEntry(logType, logEntry) {
        try {
            // Try to import SSE broadcast function
            const { sseBroadcast } = require('./routes/sse.js');

            // Add logType for SSE identification
            const sseLogEntry = { ...logEntry, logType };

            // Broadcast to specific log type channel and general logs channel
            sseBroadcast(sseLogEntry, `logs-${logType}`);
            sseBroadcast(sseLogEntry, 'logs');
        } catch (error) {
            // SSE not available or not initialized yet - this is okay during startup
        }
    }

    /**
     * Load alerts from previous session (called during startup)
     */
    loadAlertsFromFile() {
        try {
            const alertsFile = path.join(this.config.logDir, 'alerts.log');
            if (fs.existsSync(alertsFile)) {
                const content = fs.readFileSync(alertsFile, 'utf8');
                const lines = content.trim().split('\n').filter(line => line.trim());

                // Load last N entries into memory
                const maxEntries = this.config.alerts.memoryMaxEntries;
                const recentLines = lines.slice(-maxEntries);

                recentLines.forEach(line => {
                    try {
                        const logEntry = JSON.parse(line);
                        // Ensure it has the expected format
                        if (logEntry.timestamp && logEntry.message) {
                            this.memoryTransports.alerts.logs.push(logEntry);
                        }
                    } catch (e) {
                        // Skip malformed lines
                    }
                });

                console.log(`EmbeddedLogger: Loaded ${recentLines.length} alert entries from previous session`);
            }
        } catch (error) {
            console.error('EmbeddedLogger: Failed to load alerts from file:', error.message);
        }
    }
}

// Create and export the configured logger instance
const embeddedLogger = new EmbeddedSystemLogger();
module.exports = embeddedLogger;